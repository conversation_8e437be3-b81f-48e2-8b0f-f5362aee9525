# CSV to SQL Server Transformer

This Go application transforms CSV data from Azure Table Storage format into SQL Server database format, following the provided database schema.

## Features

- **Organizations**: Extracted from user partition keys and created automatically
- **Users**: Transformed from users.csv with proper role mapping
- **Projects**: Combined from books.csv and projects.csv with book URI mapping
- **AudioFiles**: Transformed from audiofiles.csv with project relationships
- **Pages**: Exported to Azure Table format (CSV) instead of SQL Server

## Prerequisites

1. **Go 1.21 or higher**
2. **SQL Server instance** (local or remote)
3. **Database with the required schema** (see schema.sql below)

## Setup

1. **Clone/Download the project**
2. **Install dependencies**:
   ```bash
   go mod tidy
   ```

3. **Update connection string** in `main.go`:
   ```go
   const connectionString = "server=your-server;user id=your-user;password=your-password;port=1433;database=your-database"
   ```

4. **Create the database schema** (run this SQL in your database):

```sql
-- Organizations
CREATE TABLE Organizations (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(255) NOT NULL,
    CreatedAt DATETIME2 DEFAULT GETDATE(),
    UpdatedAt DATETIME2 DEFAULT GETDATE()
);

-- Users
CREATE TABLE Users (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Email NVARCHAR(255) NOT NULL UNIQUE,
    UserName NVARCHAR(100) NOT NULL,
    Name NVARCHAR(255),
    EmailNotifications BIT DEFAULT 1,
    StaleProjectNotifications BIT DEFAULT 1,
    OrganizationId UNIQUEIDENTIFIER NOT NULL FOREIGN KEY REFERENCES Organizations(Id),
    Role NVARCHAR(50) CHECK (Role IN ('admin', 'member')),
    CreatedAt DATETIME2 DEFAULT GETDATE(),
    UpdatedAt DATETIME2 DEFAULT GETDATE()
);

-- Projects
CREATE TABLE Projects (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(255) NOT NULL,
    OrganizationId UNIQUEIDENTIFIER NOT NULL FOREIGN KEY REFERENCES Organizations(Id),
    BlobUri NVARCHAR(500),
    BookTitle NVARCHAR(255),
    IsTextExtracted BIT DEFAULT 0,
    IsAudioUploaded BIT DEFAULT 0,
    ThumbnailUri NVARCHAR(500),
    CreatedAt DATETIME2 DEFAULT GETDATE(),
    UpdatedAt DATETIME2 DEFAULT GETDATE()
);

-- AudioFiles
CREATE TABLE AudioFiles (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    ProjectId UNIQUEIDENTIFIER NOT NULL FOREIGN KEY REFERENCES Projects(Id),
    URI NVARCHAR(500) NOT NULL,
    FileName NVARCHAR(255),
    IsTranscoded BIT DEFAULT 0,
    UploadedByUserId UNIQUEIDENTIFIER FOREIGN KEY REFERENCES Users(Id),
    CreatedAt DATETIME2 DEFAULT GETDATE()
);
```

## Usage

1. **Place your CSV files** in the same directory as the executable:
   - `users.csv`
   - `books.csv`
   - `projects.csv`
   - `audiofiles.csv`
   - `pages.csv`

2. **Run the application**:
   ```bash
   go run main.go
   ```

## Output

The application will:

1. **Insert data into SQL Server tables**:
   - Organizations (auto-generated from user partition keys)
   - Users (with proper role mapping)
   - Projects (combined from books and projects data)
   - AudioFiles (linked to projects)

2. **Generate Azure Table format files**:
   - `pages_azure_table_format.csv` - Pages data in Azure Table format
   - `page_content_[projectid]_[pageid].txt` - Individual page content files

## Data Transformations

- **Organization Names**: Generated as "Organization_[first8chars]" from Azure partition keys
- **User Roles**: Mapped from "User"/"Admin" to "member"/"admin"
- **Project Names**: Uses project title if available, otherwise book title, otherwise generated name
- **Book Titles**: Extracted and cleaned from filenames
- **GUIDs**: New UUIDs generated for all SQL Server primary keys
- **Boolean Values**: Properly parsed from string representations

## Error Handling

- Missing or malformed CSV records are skipped with warnings
- Database connection errors will terminate the application
- Individual record insertion errors are reported but don't stop processing

## Notes

- Pages are exported to Azure Table format instead of SQL Server as requested
- New fields in the schema that don't exist in CSV data are left empty
- The application maintains relationships between entities using the new SQL Server GUIDs
