package main

import (
	"database/sql"
	"encoding/csv"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	_ "github.com/microsoft/go-mssqldb"
)

// SQL Server connection string - update with your details
const connectionString = "server=localhost;user id=sa;password=YourPassword;port=1433;database=YourDatabase"

// Struct definitions for our data
type Organization struct {
	ID        string
	Name      string
	CreatedAt time.Time
	UpdatedAt time.Time
}

type User struct {
	ID                          string
	Email                       string
	UserName                    string
	Name                        string
	EmailNotifications          bool
	StaleProjectNotifications   bool
	OrganizationID              string
	Role                        string
	CreatedAt                   time.Time
	UpdatedAt                   time.Time
}

type Project struct {
	ID               string
	Name             string
	OrganizationID   string
	BlobUri          string
	BookTitle        string
	IsTextExtracted  bool
	IsAudioUploaded  bool
	ThumbnailUri     string
	CreatedAt        time.Time
	UpdatedAt        time.Time
}

type AudioFile struct {
	ID               string
	ProjectID        string
	URI              string
	FileName         string
	IsTranscoded     bool
	UploadedByUserID string
	CreatedAt        time.Time
}

func main() {
	// Connect to SQL Server
	db, err := sql.Open("sqlserver", connectionString)
	if err != nil {
		log.Fatal("Error connecting to database:", err)
	}
	defer db.Close()

	// Test connection
	err = db.Ping()
	if err != nil {
		log.Fatal("Error pinging database:", err)
	}

	fmt.Println("Connected to SQL Server successfully!")

	// Process CSV files in order
	organizations := make(map[string]*Organization)
	projects := make(map[string]*Project)
	users := make(map[string]*User)

	// Step 1: Process users.csv to extract organizations and users
	fmt.Println("Processing users.csv...")
	err = processUsers("users.csv", db, organizations, users)
	if err != nil {
		log.Fatal("Error processing users:", err)
	}

	// Step 2: Process books.csv and projects.csv to create projects
	fmt.Println("Processing books.csv and projects.csv...")
	err = processProjects("books.csv", "projects.csv", db, organizations, projects)
	if err != nil {
		log.Fatal("Error processing projects:", err)
	}

	// Step 3: Process audiofiles.csv
	fmt.Println("Processing audiofiles.csv...")
	err = processAudioFiles("audiofiles.csv", db, projects, users)
	if err != nil {
		log.Fatal("Error processing audio files:", err)
	}

	// Step 4: Process pages.csv (output to Azure Table format)
	fmt.Println("Processing pages.csv...")
	err = processPagesToAzureTables("pages.csv", projects)
	if err != nil {
		log.Fatal("Error processing pages:", err)
	}

	fmt.Println("Data transformation completed successfully!")
}
