package main

import (
	"encoding/csv"
	"fmt"
	"os"
)

// Simple test to verify CSV files can be read
func main() {
	csvFiles := []string{"users.csv", "books.csv", "projects.csv", "audiofiles.csv", "pages.csv"}
	
	for _, filename := range csvFiles {
		fmt.Printf("\n=== Testing %s ===\n", filename)
		
		file, err := os.Open(filename)
		if err != nil {
			fmt.Printf("Error opening %s: %v\n", filename, err)
			continue
		}
		defer file.Close()

		reader := csv.NewReader(file)
		records, err := reader.ReadAll()
		if err != nil {
			fmt.Printf("Error reading %s: %v\n", filename, err)
			continue
		}

		fmt.Printf("Total records: %d\n", len(records))
		if len(records) > 0 {
			fmt.Printf("Header: %v\n", records[0])
			fmt.Printf("Columns: %d\n", len(records[0]))
		}
		if len(records) > 1 {
			fmt.Printf("First data row: %v\n", records[1])
		}
	}
}
